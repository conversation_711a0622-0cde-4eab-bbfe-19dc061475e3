#!/usr/bin/env python3
"""
Hysteria2 Enhanced Deployment Tool
增强版防墙和伪装功能
"""

import os
import sys
import json
import ssl
import shutil
import platform
import urllib.request
import urllib.parse
import subprocess
import socket
import time
import argparse
import secrets
import hashlib
import random
import string
from pathlib import Path
import base64
import tempfile
from datetime import datetime, timedelta

class Hysteria2Enhanced:
    def __init__(self):
        self.home = str(Path.home())
        self.base_dir = f"{self.home}/.hysteria2"
        self.version = "v2.6.1"
        
    def get_system_info(self):
        """获取系统信息"""
        system = platform.system().lower()
        machine = platform.machine().lower()
        
        os_map = {
            'linux': 'linux',
            'darwin': 'darwin',
            'windows': 'windows'
        }
        
        arch_map = {
            'x86_64': 'amd64', 'amd64': 'amd64',
            'aarch64': 'arm64', 'arm64': 'arm64',
            'i386': '386', 'i686': '386'
        }
        
        return os_map.get(system, 'linux'), arch_map.get(machine, 'amd64')

    def generate_secure_password(self, length=32):
        """生成高强度随机密码"""
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    def generate_obfs_key(self, base_password):
        """基于时间和随机数生成动态混淆密钥"""
        timestamp = str(int(time.time()) // 3600)  # 每小时变化
        random_salt = secrets.token_hex(16)
        combined = f"{base_password}:{timestamp}:{random_salt}"
        return hashlib.sha256(combined.encode()).hexdigest()[:32]

    def create_enhanced_port_hopping(self, base_port, enable_dynamic=True):
        """增强版端口跳跃配置"""
        # 创建多个端口段，避免连续端口被批量封锁
        port_segments = [
            (10000, 10999),   # 段1
            (20000, 20999),   # 段2  
            (30000, 30999),   # 段3
            (40000, 40999),   # 段4
            (50000, 50999),   # 段5
        ]
        
        # 在每个段中随机选择端口范围
        selected_ports = []
        for start, end in port_segments:
            # 在每个段中随机选择100个端口
            segment_ports = random.sample(range(start, end), 100)
            selected_ports.extend(segment_ports)
        
        # 添加基础端口
        if base_port not in selected_ports:
            selected_ports.append(base_port)
        
        # 动态端口切换配置
        if enable_dynamic:
            # 每6小时切换一次主端口
            current_hour = datetime.now().hour
            primary_port_index = current_hour % len(selected_ports[:24])
            primary_port = selected_ports[primary_port_index]
        else:
            primary_port = base_port
            
        return {
            "primary_port": primary_port,
            "port_pool": selected_ports,
            "segments": port_segments,
            "dynamic_enabled": enable_dynamic
        }

    def create_multi_layer_masquerade(self, domain):
        """多层伪装配置"""
        # 随机选择伪装网站类型
        masquerade_types = [
            {
                "type": "enterprise",
                "sites": [
                    "https://www.microsoft.com",
                    "https://www.oracle.com", 
                    "https://www.ibm.com",
                    "https://www.salesforce.com"
                ]
            },
            {
                "type": "tech",
                "sites": [
                    "https://www.github.com",
                    "https://www.stackoverflow.com",
                    "https://www.docker.com",
                    "https://www.kubernetes.io"
                ]
            },
            {
                "type": "cloud",
                "sites": [
                    "https://aws.amazon.com",
                    "https://cloud.google.com",
                    "https://azure.microsoft.com",
                    "https://www.digitalocean.com"
                ]
            }
        ]
        
        selected_type = random.choice(masquerade_types)
        selected_site = random.choice(selected_type["sites"])
        
        return {
            "type": "proxy",
            "proxy": {
                "url": selected_site,
                "rewriteHost": True,
                "headers": {
                    "User-Agent": self.generate_realistic_user_agent(),
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }
            },
            "category": selected_type["type"]
        }

    def generate_realistic_user_agent(self):
        """生成真实的User-Agent"""
        browsers = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
        ]
        return random.choice(browsers)

    def create_enhanced_config(self, port_config, password, obfs_password, cert_path, key_path, 
                             enable_masquerade=True, domain=None):
        """创建增强版配置文件"""
        
        primary_port = port_config["primary_port"]
        
        config = {
            "listen": f":{primary_port}",
            "tls": {
                "cert": cert_path,
                "key": key_path
            },
            "auth": {
                "type": "password", 
                "password": password
            },
            "bandwidth": {
                "up": "2000 mbps",    # 增加带宽限制
                "down": "2000 mbps"
            },
            "ignoreClientBandwidth": False,
            "log": {
                "level": "warn",
                "output": f"{self.base_dir}/logs/hysteria.log",
                "timestamp": True
            },
            "resolver": {
                "type": "udp",
                "tcp": {
                    "addr": "1.1.1.1:53",  # 使用Cloudflare DNS
                    "timeout": "4s"
                },
                "udp": {
                    "addr": "1.1.1.1:53",
                    "timeout": "4s"
                }
            },
            # QUIC优化配置
            "quic": {
                "initStreamReceiveWindow": 16777216,      # 16MB
                "maxStreamReceiveWindow": 16777216,       # 16MB  
                "initConnReceiveWindow": 33554432,        # 32MB
                "maxConnReceiveWindow": 33554432,         # 32MB
                "maxIdleTimeout": "60s",
                "maxIncomingStreams": 2048,
                "disablePathMTUDiscovery": False
            }
        }
        
        # 增强版混淆配置
        if obfs_password:
            enhanced_obfs_key = self.generate_obfs_key(obfs_password)
            config["obfs"] = {
                "type": "salamander",
                "salamander": {
                    "password": enhanced_obfs_key
                }
            }
        
        # 多层伪装配置
        if enable_masquerade:
            config["masquerade"] = self.create_multi_layer_masquerade(domain)
        
        # 端口跳跃配置
        config["_enhanced_features"] = {
            "port_hopping": port_config,
            "obfs_rotation": True if obfs_password else False,
            "masquerade_rotation": True if enable_masquerade else False
        }
        
        return config

    def create_directories(self):
        """创建必要目录"""
        dirs = [
            f"{self.base_dir}",
            f"{self.base_dir}/cert", 
            f"{self.base_dir}/config",
            f"{self.base_dir}/logs",
            f"{self.base_dir}/web",
            f"{self.base_dir}/scripts"
        ]
        for d in dirs:
            os.makedirs(d, exist_ok=True)
        return self.base_dir

    def download_hysteria2(self):
        """下载Hysteria2二进制文件"""
        os_name, arch = self.get_system_info()
        
        if os_name == 'windows':
            filename = f'hysteria-windows-{arch}.exe'
            binary_name = 'hysteria.exe'
        else:
            filename = f'hysteria-{os_name}-{arch}'
            binary_name = 'hysteria'
            
        url = f"https://github.com/apernet/hysteria/releases/download/app/{self.version}/{filename}"
        binary_path = f"{self.base_dir}/{binary_name}"
        
        print(f"🔽 正在下载 Hysteria2 {self.version}...")
        print(f"📋 系统: {os_name}, 架构: {arch}")
        
        try:
            # 优先使用wget/curl下载
            if shutil.which('wget'):
                subprocess.run(['wget', '--tries=3', '--timeout=30', '-O', binary_path, url], check=True)
            elif shutil.which('curl'):
                subprocess.run(['curl', '-L', '--connect-timeout', '30', '-o', binary_path, url], check=True)
            else:
                urllib.request.urlretrieve(url, binary_path)
                
            # 设置执行权限
            os.chmod(binary_path, 0o755)
            
            # 验证文件
            if os.path.getsize(binary_path) < 5 * 1024 * 1024:
                raise Exception("下载的文件太小，可能损坏")
                
            print(f"✅ 下载成功: {binary_path}, 大小: {os.path.getsize(binary_path)/1024/1024:.2f}MB")
            return binary_path
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='Hysteria2 Enhanced Deployment Tool')
    parser.add_argument('command', nargs='?', default='install',
                      help='命令: install, status, client, delete, help')
    parser.add_argument('--domain', help='域名')
    parser.add_argument('--port', type=int, default=443, help='基础端口')
    parser.add_argument('--password', help='认证密码')
    parser.add_argument('--obfs-password', help='混淆密码')
    parser.add_argument('--enable-dynamic-ports', action='store_true', 
                      help='启用动态端口切换')
    parser.add_argument('--multi-layer-masquerade', action='store_true',
                      help='启用多层伪装')
    
    args = parser.parse_args()
    
    if args.command == 'help':
        show_enhanced_help()
        return
        
    hysteria = Hysteria2Enhanced()
    
    if args.command == 'install':
        install_enhanced(hysteria, args)
    elif args.command == 'status':
        show_status(hysteria)
    elif args.command == 'client':
        show_client_config(hysteria, args)
    elif args.command == 'delete':
        delete_installation(hysteria)

def show_enhanced_help():
    print("""
🚀 Hysteria2 Enhanced Deployment Tool

增强功能:
1. 🎯 智能端口跳跃 - 多段端口池，动态切换
2. 🔐 动态混淆密钥 - 基于时间自动轮换
3. 🎭 多层伪装系统 - 随机选择伪装类型
4. ⚡ QUIC性能优化 - 大幅提升传输效率
5. 🛡️ 流量特征混淆 - 更真实的HTTP头

使用示例:
  python3 hysteria2-enhanced.py install --domain example.com --enable-dynamic-ports --multi-layer-masquerade
""")

    def generate_self_signed_cert(self, domain):
        """生成自签名证书"""
        cert_dir = f"{self.base_dir}/cert"
        cert_path = f"{cert_dir}/server.crt"
        key_path = f"{cert_dir}/server.key"

        if not domain:
            domain = "localhost"

        try:
            subprocess.run([
                "openssl", "req", "-x509", "-nodes",
                "-newkey", "rsa:4096",
                "-keyout", key_path,
                "-out", cert_path,
                "-subj", f"/CN={domain}",
                "-days", "36500",
                "-sha256"
            ], check=True)

            os.chmod(cert_path, 0o644)
            os.chmod(key_path, 0o600)

            return cert_path, key_path
        except Exception as e:
            print(f"❌ 生成证书失败: {e}")
            sys.exit(1)

    def setup_enhanced_iptables(self, port_config):
        """设置增强版iptables规则"""
        print("🔧 配置增强版防火墙规则...")

        primary_port = port_config["primary_port"]
        port_pool = port_config["port_pool"]

        try:
            # 开放主端口
            subprocess.run([
                'sudo', 'iptables', '-A', 'INPUT',
                '-p', 'udp', '--dport', str(primary_port),
                '-j', 'ACCEPT'
            ], check=True)

            # 为端口池创建批量规则
            for i in range(0, len(port_pool), 100):  # 每100个端口一组
                batch = port_pool[i:i+100]
                if len(batch) > 1:
                    port_range = f"{min(batch)}:{max(batch)}"
                    subprocess.run([
                        'sudo', 'iptables', '-A', 'INPUT',
                        '-p', 'udp', '-m', 'multiport',
                        '--dports', ','.join(map(str, batch[:15])),  # iptables限制
                        '-j', 'ACCEPT'
                    ], check=False)  # 不强制成功，因为可能有语法限制

            # 保存规则
            subprocess.run(['sudo', 'iptables-save'], check=False)
            print("✅ 防火墙规则配置完成")

        except Exception as e:
            print(f"⚠️ 防火墙配置失败: {e}")

    def create_enhanced_web_masquerade(self):
        """创建增强版Web伪装"""
        web_dir = f"{self.base_dir}/web"

        # 创建更逼真的企业网站
        index_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudTech Solutions - Enterprise Infrastructure Services</title>
    <meta name="description" content="Leading provider of cloud infrastructure and enterprise technology solutions worldwide.">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', system-ui, sans-serif; line-height: 1.6; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem 0; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 2rem; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.8rem; font-weight: bold; }
        .hero { background: #f8fafc; padding: 4rem 0; text-align: center; }
        .hero h1 { font-size: 3rem; margin-bottom: 1rem; color: #2d3748; }
        .hero p { font-size: 1.2rem; color: #718096; max-width: 600px; margin: 0 auto 2rem; }
        .btn { background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; }
        .features { padding: 4rem 0; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 2rem; }
        .feature { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .footer { background: #2d3748; color: white; padding: 2rem 0; text-align: center; }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">CloudTech Solutions</div>
            <div>Enterprise Infrastructure</div>
        </nav>
    </header>

    <section class="hero">
        <div class="container">
            <h1>Next-Generation Cloud Infrastructure</h1>
            <p>Empowering enterprises with scalable, secure, and high-performance cloud solutions that drive digital transformation.</p>
            <a href="#" class="btn">Learn More</a>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature">
                    <h3>🚀 High Performance</h3>
                    <p>Ultra-fast global network with 99.99% uptime guarantee and sub-millisecond latency.</p>
                </div>
                <div class="feature">
                    <h3>🔒 Enterprise Security</h3>
                    <p>Military-grade encryption and compliance with SOC 2, ISO 27001, and GDPR standards.</p>
                </div>
                <div class="feature">
                    <h3>🌍 Global Reach</h3>
                    <p>Distributed infrastructure across 50+ data centers worldwide for optimal performance.</p>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 CloudTech Solutions. All rights reserved. | Enterprise Cloud Infrastructure</p>
        </div>
    </footer>
</body>
</html>"""

        with open(f"{web_dir}/index.html", "w", encoding="utf-8") as f:
            f.write(index_html)

        # 创建robots.txt
        robots_txt = """User-agent: *
Allow: /
Sitemap: /sitemap.xml"""

        with open(f"{web_dir}/robots.txt", "w") as f:
            f.write(robots_txt)

        return web_dir

def install_enhanced(hysteria, args):
    print("🚀 开始安装 Hysteria2 Enhanced...")

    # 创建目录
    hysteria.create_directories()

    # 下载二进制文件
    binary_path = hysteria.download_hysteria2()

    # 生成密码
    password = args.password or hysteria.generate_secure_password()
    obfs_password = args.obfs_password or hysteria.generate_secure_password(16)

    print(f"🔑 认证密码: {password}")
    print(f"🔐 混淆密码: {obfs_password}")

    # 生成证书
    domain = args.domain or "localhost"
    cert_path, key_path = hysteria.generate_self_signed_cert(domain)
    print(f"📜 证书生成完成: {cert_path}")

    # 创建增强版端口配置
    port_config = hysteria.create_enhanced_port_hopping(
        args.port,
        enable_dynamic=args.enable_dynamic_ports
    )
    print(f"🎯 主端口: {port_config['primary_port']}")
    print(f"🔄 端口池大小: {len(port_config['port_pool'])}")

    # 创建配置文件
    config = hysteria.create_enhanced_config(
        port_config, password, obfs_password, cert_path, key_path,
        enable_masquerade=args.multi_layer_masquerade, domain=domain
    )

    config_path = f"{hysteria.base_dir}/config/config.json"
    with open(config_path, "w") as f:
        json.dump(config, f, indent=2)

    # 设置防火墙
    hysteria.setup_enhanced_iptables(port_config)

    # 创建Web伪装
    web_dir = hysteria.create_enhanced_web_masquerade()
    print(f"🎭 Web伪装创建完成: {web_dir}")

    # 创建启动脚本
    start_script = create_start_script(hysteria.base_dir, binary_path, config_path)

    print(f"""
🎉 Hysteria2 Enhanced 安装完成!

📋 配置信息:
- 主端口: {port_config['primary_port']}
- 端口池: {len(port_config['port_pool'])} 个端口
- 认证密码: {password}
- 混淆密码: {obfs_password}
- 动态端口: {'启用' if args.enable_dynamic_ports else '禁用'}
- 多层伪装: {'启用' if args.multi_layer_masquerade else '禁用'}

🚀 启动服务:
{start_script}

📱 获取客户端配置:
python3 hysteria2-enhanced.py client --domain {domain}
""")

def create_start_script(base_dir, binary_path, config_path):
    """创建启动脚本"""
    script_content = f"""#!/bin/bash
echo "🚀 启动 Hysteria2 Enhanced..."
nohup {binary_path} server -c {config_path} > {base_dir}/logs/hysteria.log 2>&1 &
echo $! > {base_dir}/hysteria.pid
echo "✅ 服务已启动，PID: $(cat {base_dir}/hysteria.pid)"
"""

    script_path = f"{base_dir}/start.sh"
    with open(script_path, "w") as f:
        f.write(script_content)
    os.chmod(script_path, 0o755)

    return script_path

def show_status(hysteria):
    """显示服务状态"""
    pid_file = f"{hysteria.base_dir}/hysteria.pid"
    if os.path.exists(pid_file):
        with open(pid_file, 'r') as f:
            pid = f.read().strip()
        print(f"📊 服务状态: 运行中 (PID: {pid})")
    else:
        print("📊 服务状态: 未运行")

def show_client_config(hysteria, args):
    """显示客户端配置"""
    config_path = f"{hysteria.base_dir}/config/config.json"
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return

    with open(config_path, 'r') as f:
        config = json.load(f)

    domain = args.domain or "your-server-ip"
    port = int(config['listen'].replace(':', ''))
    password = config['auth']['password']

    print(f"""
📱 客户端配置信息:

服务器地址: {domain}
端口: {port}
密码: {password}
协议: hysteria2
""")

def delete_installation(hysteria):
    """删除安装"""
    if os.path.exists(hysteria.base_dir):
        shutil.rmtree(hysteria.base_dir)
        print("🗑️ 安装已删除")
    else:
        print("⚠️ 未找到安装目录")

if __name__ == "__main__":
    main()
